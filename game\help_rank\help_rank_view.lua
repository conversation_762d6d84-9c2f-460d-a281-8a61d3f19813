HelpRankView = HelpRankView or BaseClass(SafeBaseView)

function HelpRankView:__init()
    self.view_layer = UiLayer.Normal
    -- self.view_style = ViewStyle.Half
	self.default_index = 10
    self:SetMaskBg()
	-- self:AddViewResource(0, ResPath.CommonBundleName, "layout_a3_common_activity_panel")
    self:AddViewResource(0, "uis/view/help_rank_ui_prefab", "layout_help_rank")
	--self:AddViewResource(0, ResPath.CommonBundleName, "VerticalTabbar_Activity")
	-- self:AddViewResource(0, ResPath.CommonBundleName, "layout_a3_light_common_top_panel")
end

function HelpRankView:__delete()
end

function HelpRankView:ReleaseCallBack()
	-- if self.tabbar then
	-- 	self.tabbar:DeleteMe()
	-- 	self.tabbar = nil
	-- end

	if self.money_bar then
		self.money_bar:DeleteMe()
		self.money_bar = nil
	end

	if self.item_list then
        self.item_list:DeleteMe()
        self.item_list = nil
    end

    if self.model_display then
        self.model_display:DeleteMe()
        self.model_display = nil
	end

	if self.rank_sel_btn_list then
		self.rank_sel_btn_list:DeleteMe()
		self.rank_sel_btn_list = nil
	end

	if self.soul_ring_model then
        self.soul_ring_model:DeleteMe()
        self.soul_ring_model = nil
    end

    if CountDownManager.Instance:HasCountDown("help_rank_time") then
        CountDownManager.Instance:RemoveCountDown("help_rank_time")
    end

	self.cur_sel_rank_cel_index = nil
	self.cur_select_shop_type = nil
	self.cur_show_soul_ring_id = nil
	self.cur_model_show_itemid = nil
	self.need_replay_anim = nil
end

function HelpRankView:CloseCallBack()
	self.need_replay_anim = true
	self.cur_model_show_itemid = nil
end

function HelpRankView:OpenCallBack()
	-- HelpRankWGCtrl.Instance:ReqHelpRankInfo(OA_HELP_RANK_OPERATE_TYPE.INFO)
	if self.need_replay_anim then
		TweenManager.Instance:ExecuteViewTween(self.view_name, 0, self.node_list)
	end
end

function HelpRankView:LoadCallBack()
	-- self.node_list.title_view_name.text.text = Language.HelpRank.TitleName
	local title_name = HelpRankWGData.Instance:GetCurShopTitle()
	local bundle, asset = ResPath.GetRawImagesPNG(title_name)
	if self.node_list.raw_title then
		self.node_list["raw_title"].raw_image:LoadSprite(bundle, asset, function()
			self.node_list["raw_title"].raw_image:SetNativeSize()
		end)
	end

	if not self.rank_sel_btn_list then
        self.rank_sel_btn_list = AsyncListView.New(HelpRankSelectBtnItem, self.node_list["rank_sel_btn_list"])
		self.rank_sel_btn_list:SetSelectCallBack(BindTool.Bind(self.OnClickRankSelBtn, self))
	end

	-- self:InitMoneyBar()
	--self:InitTabbar()

	if not self.item_list then
        self.item_list = AsyncListView.New(HelpRankRender, self.node_list["ph_reward_list"])
    end

    if not self.model_display then
        self.model_display = OperationActRender.New(self.node_list["display_model"])
        self.model_display:SetModelType(MODEL_CAMERA_TYPE.BASE, MODEL_OFFSET_TYPE.NORMALIZE)
	end

	if not self.soul_ring_model then
		self.soul_ring_model = RoleModel.New()
		local display_data = {
			parent_node = self.node_list["soul_ring_model"],
			camera_type = MODEL_CAMERA_TYPE.BASE,
			rt_scale_type = ModelRTSCaleType.L,
			can_drag = false,
		}

		self.soul_ring_model:SetRenderTexUI3DModel(display_data)

		local role_vo = GameVoManager.Instance:GetMainRoleVo()
		local special_status_table = {ignore_wing = true, ignore_halo = true, ignore_fazhen = true, ignore_mantle = true, ignore_tail = true, ignore_jianzhen = true}
		self.soul_ring_model:SetModelResInfo(role_vo, special_status_table)
    end

	XUI.AddClickEventListener(self.node_list["buy_btn"], BindTool.Bind(self.OnClickBuy, self))
	self:RegisterDefaultClose()
    -- XUI.AddClickEventListener(self.node_list["daliy_free_gift_btn"], BindTool.Bind(self.OnClickGetFreeRewardBtn, self))
	for i = 1, 3 do
		self.node_list["tog_type_" .. i].toggle:AddValueChangedListener(BindTool.Bind(self.OnClickSelectTypeTog, self, i))
    end
	self.cur_sel_rank_cel_index = nil
end

function HelpRankView:ShowIndexCallBack()
	ReportManager:ReportBuriedEvent(BURIED_EVENT_LOG_ID.actClick, GuideModuleName.HelpRankView, ACTIVITY_TYPE.RAND_ACTIVITY_TYPE_OA_HELP_RANK)
	HelpRankWGData.Instance:SetHelpRankOpenFlag()
	RemindManager.Instance:Fire(RemindName.HelpRankRed)
end

function HelpRankView:InitMoneyBar()
	if not self.money_bar then
		self.money_bar = MoneyBar.New()
		local bundle, asset = ResPath.GetWidgets("MoneyBar")
		local show_params = {
			show_gold = true,
			show_bind_gold = true,
			show_coin = true,
			show_silver_ticket = true,
			show_cangjin_score = true,
		}
		self.money_bar:SetMoneyShowInfo(0, 0, show_params)
		self.money_bar:LoadAsset(bundle, asset, self.node_list["money_tabar_pos"].transform)
	end
end

function HelpRankView:OnFlush(param_t, index)
	local shop_info = HelpRankWGData.Instance:GetAllShopCfgGroup()
	if IsEmptyTable(shop_info) then
		return
	end

	self.rank_sel_btn_list:SetDataList(shop_info)
	self.rank_sel_btn_list:SelectIndex(self.cur_sel_rank_cel_index or 1)
	
	self.node_list["left_sel_list"]:SetActive(#shop_info > 1) --只有单个冲榜时隐藏页签
end

function HelpRankView:OnClickRankSelBtn(cell, index)
	-- if self.cur_sel_rank_cel_index == index then
	-- 	return
	-- end
	self.cur_sel_rank_cel_index = index
	local shop_data = cell:GetData()
	self.cur_sel_rank_data = shop_data

	local show_shop_type
	if self.cur_select_shop_type and shop_data[self.cur_select_shop_type] then
		show_shop_type = self.cur_select_shop_type
	else
		for i = 1, 3 do
			if shop_data[i] then
				show_shop_type = show_shop_type or i
				self.node_list["tog_type_" .. i]:SetActive(true)
			else
				self.node_list["tog_type_" .. i]:SetActive(false)
			end
		end
		show_shop_type = show_shop_type or 1
	end

	self.node_list["tog_type_" .. show_shop_type].toggle.isOn = true
	self.cur_select_shop_type = show_shop_type

	self:FlushShop(shop_data[show_shop_type] or {})
	
	-- 礼包类型小于两个就不显示切换toggle
	local show_shop_count = 0
	for i = 1, 3 do
		if shop_data[i] then
			show_shop_count = show_shop_count + 1
		end
	end
	self.node_list["toggle_group"]:SetActive(show_shop_count > 1)
end

function HelpRankView:OnClickSelectTypeTog(index, is_on)
	if IsEmptyTable(self.cur_sel_rank_data) or not is_on then
		return
	end
	self.cur_select_shop_type = index
	self:FlushShop(self.cur_sel_rank_data[index] or {})
end

function HelpRankView:FlushShop(shop_info)
	if IsEmptyTable(shop_info) then
		return
	end

	local cfg = shop_info[1].cfg
	local cur_grade_info = cfg
	self:FlushModel(cur_grade_info)
    self.item_list:SetDataList(shop_info)
	
	if cur_grade_info.type == HELP_RANK_SHOP_TYPE.LINGYU then -- 灵玉
		self.node_list["buy_btn"]:SetActive(false)
	elseif cur_grade_info.type == HELP_RANK_SHOP_TYPE.PURCHASE then -- 直购
		local is_can_all_buy = true
		local all_buy_cfg = HelpRankWGData.Instance:GetCurAllBuyShopCfg(cur_grade_info.rank_seq)
		if all_buy_cfg.is_show == 0 then
			is_can_all_buy = false
		else
			for i, v in ipairs(shop_info) do
				local shop_cfg = v.cfg
				local buy_count = HelpRankWGData.Instance:GetShopBuyCountBySeq(shop_cfg.rank_seq, shop_cfg.seq)
				local is_buy = buy_count >= tonumber(shop_cfg.buy_limit)
				if is_buy then
					is_can_all_buy = false
					break
				end
			end
		end
		self.node_list["buy_btn"]:CustomSetActive(is_can_all_buy)
		if is_can_all_buy then
			local price = RoleWGData.GetPayMoneyStr(all_buy_cfg.price, all_buy_cfg.rmb_type, all_buy_cfg.rmb_seq)
			local is_not_all_buy = not HelpRankWGData.Instance:GetCurShopIsAllBuy()
			self.node_list.buy_text.text.text = is_not_all_buy and string.format(Language.TianShenPurchase.OnceBuy, price) or Language.HelpRank.YSX
			local discount = all_buy_cfg.discount
			self.node_list.discount_img:SetActive(discount ~= "")
			self.node_list.discount_text.text.text = string.format(Language.HelpRank.DisCount, discount)
		end
	end
	
	-- local free_reward_flag = HelpRankWGData.Instance:GetFreeRewardFlag()
	-- self.node_list["daliy_free_gift_btn"]:SetActive(free_reward_flag == 0)
    -- self.node_list.free_reward_red:SetActive(free_reward_flag == 0)

	self:FlushTimeCount()
end

-- 是否显示一键购买
-- function HelpRankView:IsCanAllBuy(shop_list)
-- 	local is_can_all_buy = true
-- 	for i, v in ipairs(shop_info) do
-- 		if HelpRankWGData.Instance:GetCurShopIsBuyBySeq(v.seq) then
-- 			return false
-- 		end
-- 	end
-- end

function HelpRankView:FlushModel(shop_info)
	local pos_x, pos_y = 0, 0
	if shop_info.display_pos and shop_info.display_pos ~= "" then
		local pos_list = string.split(shop_info.display_pos, "|")
		pos_x = tonumber(pos_list[1]) or pos_x
		pos_y = tonumber(pos_list[2]) or pos_y
	end

	local scale = tonumber(shop_info["display_scale"]) or 0

	if shop_info.soul_ring_id and shop_info.soul_ring_id ~= ""  then
		if self.cur_show_soul_ring_id == shop_info.soul_ring_id then
			return
		end
		self.cur_show_soul_ring_id = shop_info.soul_ring_id
		self.node_list.soul_ring_model:SetActive(true)
        self.node_list.display_model:SetActive(false)
		self.node_list["common_capability"]:SetActive(false)
		self.node_list["common_capability_max"]:SetActive(false)
		if shop_info.rotation and "" ~= shop_info.rotation then
			local pos_list = string.split(shop_info.rotation, "|")
			local x = tonumber(pos_list[1]) or 0
			local y = tonumber(pos_list[2]) or 0
			local z = tonumber(pos_list[3]) or 0

			self.soul_ring_model:SetRTAdjustmentRootLocalRotation(x, y, z)
		end

		self.soul_ring_model:SetRTAdjustmentRootLocalScale(scale, scale, scale)

		RectTransform.SetAnchoredPositionXY(self.node_list["soul_ring_model"].rect, pos_x, pos_y)

        local target_data = {}
        if shop_info.soul_ring_id and "" ~= shop_info.soul_ring_id then
			local soul_ring_id_list = string.split(shop_info.soul_ring_id, "|")
            for k, v in pairs(soul_ring_id_list) do
                local cfg = ShenShouWGData.Instance:GetShenShouCfg(tonumber(v))
                target_data[k - 1] = {soul_ring_effect = cfg.soul_ring_effect}
            end

            self.soul_ring_model:SetTotalSoulRingResid(target_data, false, #soul_ring_id_list)
		end
	else
		if self.cur_model_show_itemid == shop_info.model_show_itemid then
			return
		end

		self.cur_model_show_itemid = shop_info.model_show_itemid
		self.node_list.soul_ring_model:SetActive(false)
        self.node_list.display_model:SetActive(true)
		local display_data = {}
		display_data.should_ani = true
		display_data.model_rt_type = ModelRTSCaleType.XL
		if shop_info.model_show_itemid ~= 0 and shop_info.model_show_itemid ~= "" then
			local split_list = string.split(shop_info.model_show_itemid, "|")
			if #split_list > 1 then
				local list = {}
				for k, v in pairs(split_list) do
					list[tonumber(v)] = true
				end
				display_data.model_item_id_list = list
			else
				display_data.item_id = shop_info.model_show_itemid
			end
		end
		display_data.bundle_name = shop_info["model_bundle_name"]
		display_data.asset_name = shop_info["model_asset_name"]
		display_data.image_effect_bundle = shop_info.image_effect_bundle
		display_data.image_effect_asset = shop_info.image_effect_asset
		local model_show_type = tonumber(shop_info["model_show_type"]) or 1
		display_data.render_type = model_show_type - 1
		if shop_info["open_item_id"] ~= "" then
			display_data.model_click_func = function ()
				TipWGCtrl.Instance:OpenItem({item_id = shop_info["open_item_id"]})
			end
		end

		RectTransform.SetAnchoredPositionXY(self.node_list["display_model"].rect, pos_x, pos_y)

		if shop_info.display_rotation and shop_info.display_rotation ~= "" then
			local rot_list = string.split(shop_info.display_rotation, "|")
			local rot_x = tonumber(rot_list[1]) or 0
			local rot_y = tonumber(rot_list[2]) or 0
			local rot_z = tonumber(rot_list[3]) or 0

			display_data.model_adjust_root_local_rotation = Vector3(rot_x, rot_y, rot_z)
		end

		display_data.model_adjust_root_local_scale = scale

		self.model_display:SetData(display_data)

		-- Transform.SetLocalScaleXYZ(self.node_list["display_model"].transform, scale, scale, scale)



		-- 战力
		local capability, show_max_cap, _ = 0, false, nil
		local show_item_id = display_data.item_id
		if show_item_id then
			if ItemWGData.GetIsXiaogGui(show_item_id) then
				_, capability = ItemShowWGData.Instance:GetShouHuAttrByData(show_item_id)
			elseif HiddenWeaponWGData.Instance:GetOnlyEquipCfgById(show_item_id) then
				capability = HiddenWeaponWGData.Instance:GetItemCapability(show_item_id)
			else
				local item_cfg = ItemWGData.Instance:GetItemConfig(show_item_id)
				if item_cfg then
					local need_get_sys, sys_type = ItemShowWGData.Instance:GetIsNeedSysAttr(show_item_id, item_cfg.sys_attr_cap_location)
					if sys_type == ITEMTIPS_SYSTEM.MOUNT or sys_type == ITEMTIPS_SYSTEM.LING_CHONG
					or sys_type == ITEMTIPS_SYSTEM.HUA_KUN or sys_type == ITEMTIPS_SYSTEM.TS_HUANHUA
					or sys_type == ITEMTIPS_SYSTEM.XIAN_WA then
						show_max_cap = true
						capability = ItemShowWGData.CalculateCapability(show_item_id, show_max_cap) or 0
					else
						capability = ItemShowWGData.CalculateCapability(show_item_id) or 0
					end
				end
			end
		end

		self.node_list["cap_value"].text.text = capability
		self.node_list["max_cap_value"].text.text = capability
		self.node_list["common_capability"]:SetActive(capability > 0 and not show_max_cap)
		self.node_list["common_capability_max"]:SetActive(capability > 0 and show_max_cap)
	end
end

function HelpRankView:OnClickBuy()
	if not self.cur_sel_rank_data then
		return
	end

	local all_buy_cfg = HelpRankWGData.Instance:GetCurAllBuyShopCfg(self.cur_sel_rank_data.rank_seq)
	if not IsEmptyTable(all_buy_cfg) then
		RechargeWGCtrl.Instance:Recharge(all_buy_cfg.price, all_buy_cfg.rmb_type, all_buy_cfg.rmb_seq)
	end
end

function HelpRankView:OnClickGetFreeRewardBtn()
	-- local free_reward_flag = HelpRankWGData.Instance:GetFreeRewardFlag()
	-- if free_reward_flag == 0 then
	-- 	HelpRankWGCtrl.Instance:ReqHelpRankInfo(OA_HELP_RANK_OPERATE_TYPE.FREE_GIFT)
	-- end
end

function HelpRankView:FlushTimeCount()
    local time = TimeUtil.GetTodayRestTime(TimeWGCtrl.Instance:GetServerTime())
    if time > 0 then
        if CountDownManager.Instance:HasCountDown("help_rank_time") then
            CountDownManager.Instance:RemoveCountDown("help_rank_time")
        end

        CountDownManager.Instance:AddCountDown("help_rank_time",
            BindTool.Bind(self.FinalUpdateTimeCallBack, self),
            BindTool.Bind(self.OnComplete, self),
            nil, time, 1)
    else
        self:OnComplete()
    end
end

function HelpRankView:FinalUpdateTimeCallBack(now_time, total_time)
    local time = math.ceil(total_time - now_time)
    local time_str = TimeUtil.FormatSecondDHM8(time)
    self.node_list["time_str"].text.text = string.format(Language.HelpRank.ActivityTime, time_str)
end

function HelpRankView:OnComplete()
    self.node_list.time_str.text.text = ""
end

------------------------------
HelpRankRender = HelpRankRender or BaseClass(BaseRender)
local seq2cn = {
	[0] = "一",
	[1] = "二",
	[2] = "三",
}

function HelpRankRender:LoadCallBack()
	self.big_award = HelpRankItem.New(self.node_list.big_award)

	if self.reward_list == nil then
		self.reward_list = AsyncListView.New(HelpRankItem, self.node_list.reward_list)
		self.reward_list:SetStartZeroIndex(false)
	end

	XUI.AddClickEventListener(self.node_list["help_rank_buy_btn"], BindTool.Bind(self.OnClickBuyGift, self))
	self.is_play_anim = false
end

function HelpRankRender:__delete()
	if self.big_award then
		self.big_award:DeleteMe()
		self.big_award = nil
	end

	if self.reward_list then
		self.reward_list:DeleteMe()
		self.reward_list = nil
	end

	if self.buy_remind_alert ~= nil then
		self.buy_remind_alert:DeleteMe()
		self.buy_remind_alert = nil
	end

	self.is_play_anim = nil
end

function HelpRankRender:OnFlush()
	if not self.data then
		return
	end

	local cfg = self.data.cfg

	self.node_list.title_name.text.text = cfg.sign_des1

	local is_buy = self.data.is_buy

	-- self.node_list["txt_unlock_desc"]:SetActive(cfg.type == HELP_RANK_SHOP_TYPE.LINGYU)
	self.node_list["buy_btn_text"]:SetActive(cfg.type == HELP_RANK_SHOP_TYPE.PURCHASE)
	self.node_list["buy_btn_lingyu_price"]:SetActive(cfg.type == HELP_RANK_SHOP_TYPE.LINGYU or cfg.type == HELP_RANK_SHOP_TYPE.SCORE)

	if cfg.type == HELP_RANK_SHOP_TYPE.LINGYU then
		self.node_list["buy_btn_lingyu_price"].text.text = cfg.price --购买价格
		local icon = cfg.buy_type == 1 and "a3_huobi_xianyu" or "a3_huobi_bangyu"
		local bundle, asset = ResPath.GetCommonIcon(icon)
		self.node_list["cost_icon"].image:LoadSprite(bundle, asset)
		local lingyu_buy_data = HelpRankWGData.Instance:GetLingYuShopBuyData(cfg.rank_seq)
		-- self.node_list["txt_unlock_desc"]:SetActive(cfg.seq > lingyu_buy_data.seq)
		-- self.node_list["txt_unlock_desc"].text.text = cfg.unlock_desc
		local had_buy_count = 0
		if cfg.seq < lingyu_buy_data.seq then
			had_buy_count = cfg.buy_limit
		elseif cfg.seq == lingyu_buy_data.seq then
			had_buy_count = lingyu_buy_data.buy_count
		end
		-- self.node_list["txt_limit_buy_count"].text.text = string.format(Language.HelpRank.LimitBuy, cfg.buy_limit - had_buy_count, cfg.buy_limit)
	elseif cfg.type == HELP_RANK_SHOP_TYPE.PURCHASE then
		local buy_count = HelpRankWGData.Instance:GetShopBuyCountBySeq(cfg.rank_seq, cfg.seq)
		-- self.node_list["txt_limit_buy_count"].text.text = string.format(Language.HelpRank.LimitBuy, cfg.buy_limit - buy_count, cfg.buy_limit)
		local price = RoleWGData.GetPayMoneyStr(cfg.price, cfg.rmb_type, cfg.rmb_seq)
		self.node_list["buy_btn_text"].text.text = price --购买价格
	elseif cfg.type == HELP_RANK_SHOP_TYPE.SCORE then
		self.node_list["buy_btn_lingyu_price"].text.text = cfg.price --购买价格
		local icon = COMMON_CONSTS.VIRTUAL_ITEM_CANG_JIN_SCORE
		local bundle, asset = ResPath.GetItem(icon)
		self.node_list["cost_icon"].image:LoadSprite(bundle, asset)
		local buy_count = HelpRankWGData.Instance:GetScoreShopBuyCountBySeq(cfg.rank_seq, cfg.seq)
		-- self.node_list["txt_limit_buy_count"].text.text = string.format(Language.HelpRank.LimitBuy, cfg.buy_limit - buy_count, cfg.buy_limit)
	end

	self.node_list["is_buy_img"]:SetActive(is_buy)
	self.node_list["help_rank_buy_btn"]:SetActive(not is_buy)

	local item_data = {}

	for k, v in pairs(cfg.reward_item) do -- 物品展示
		item_data[k] = {reward_item = v, is_buy = is_buy}
	end
	self.big_award:SetData(item_data[0])
	self.reward_list:SetDataList(item_data)
	-- 折扣
	self.node_list["discount_text"].text.text = string.format(Language.HelpRank.DisCount, cfg.discount_gift)
	-- 描述
	-- self.node_list.help_rank_lable1.text.text = cfg.sign_des1
	-- self.node_list.help_rank_lable2.text.text = cfg.sign_des2
	-- self.node_list.help_rank_lable3.text.text = cfg.sign_des3
	-- self.node_list.lable1:SetActive(cfg.sign_des1 ~= "")
	-- self.node_list.lable2:SetActive(cfg.sign_des2 ~= "")
	-- self.node_list.lable3:SetActive(cfg.sign_des3 ~= "")

	-- if not self.is_play_anim then
	-- 	self.is_play_anim = true
	-- 	self:DoLableAnim()
	-- end
end

function HelpRankRender:OnClickBuyGift()
	if not self.data then
		return
	end

	local cfg = self.data.cfg

	local role_gold = GameVoManager.Instance:GetMainRoleVo().gold
	local cur_score = YanYuGeWGData.Instance:GetCurScore() -- 
	if nil == self.buy_remind_alert then
		self.buy_remind_alert = Alert.New(nil, nil, nil, nil, true)
		self.buy_remind_alert:SetShowCheckBox(true, "help_rank")
		self.buy_remind_alert:SetCheckBoxDefaultSelect(false)
	end

	if cfg.type == HELP_RANK_SHOP_TYPE.PURCHASE then --直购
		local price = RoleWGData.GetPayMoneyStr(cfg.price, cfg.rmb_type, cfg.rmb_seq)
		self.buy_remind_alert:SetLableString(string.format(Language.HelpRank.BuyShopTips, price))
		self.buy_remind_alert:SetOkFunc(function()
			RechargeWGCtrl.Instance:Recharge(cfg.price, cfg.rmb_type, cfg.rmb_seq)
		end)
	elseif cfg.type == HELP_RANK_SHOP_TYPE.LINGYU then --灵玉
		local lingyu_buy_data = HelpRankWGData.Instance:GetLingYuShopBuyData(cfg.rank_seq)
		if cfg.seq > lingyu_buy_data.seq then --未解锁
			TipWGCtrl.Instance:ShowSystemMsg(cfg.unlock_desc)
			return
		end
		local cost_value = cfg.price or 0
		self.buy_remind_alert:SetLableString(string.format(Language.HelpRank.BuyShopTips1, cost_value))
		self.buy_remind_alert:SetOkFunc(function()
			if role_gold < cost_value then
				VipWGCtrl.Instance:OpenTipNoGold()
			else
				HelpRankWGCtrl.Instance:ReqHelpRankInfo(OA_HELP_RANK_OPERATE_TYPE.BUY, cfg.rank_seq, cfg.seq, cfg.type)
			end
		end)
	elseif cfg.type == HELP_RANK_SHOP_TYPE.SCORE then --积分
		local cost_value = cfg.price or 0
		self.buy_remind_alert:SetLableString(string.format(Language.HelpRank.BuyShopTips2, cost_value))
		self.buy_remind_alert:SetOkFunc(function()
			if cur_score < cost_value then
				TipWGCtrl.Instance:ShowSystemMsg(Language.YanYuGe.NoEnoughScore)
				RechargeWGCtrl.Instance:RemindRechargeByCangJinShangPuScoreNoEnough(cost_value - cur_score)
			else
				HelpRankWGCtrl.Instance:ReqHelpRankInfo(OA_HELP_RANK_OPERATE_TYPE.BUY, cfg.rank_seq, cfg.seq, cfg.type)
			end
		end)
	end

	self.buy_remind_alert:Open()
end

function HelpRankRender:DoLableAnim()
	local delay_time_count = 1

	for i = 1, 3 do
		local node = self.node_list["lable" .. i]
		local active = node:GetActive()

		if active then
			ReDelayCall(self, function()
				if node then
					node:SetActive(true)
					UITween.DoUpDownCrashTween(node)
				end
			end, 0.3 * delay_time_count, "help_rank_render_lable_" .. self.index .. i)
			delay_time_count = delay_time_count + 1
		end
	end
end




------------------
HelpRankItem = HelpRankItem or BaseClass(BaseRender)

function HelpRankItem:LoadCallBack()
	self.item_cell = ItemCell.New(self.node_list.item_pos)
end

function HelpRankItem:__delete()
	if self.item_cell then
		self.item_cell:DeleteMe()
		self.item_cell = nil
	end
end

function HelpRankItem:OnFlush()
    if not self.data then
        return
    end

    self.item_cell:SetData(self.data.reward_item)
    --self.node_list.mask:SetActive(self.data.is_lock or self.data.is_buy)
    --self.node_list.lock:SetActive(self.data.is_lock)
	self.node_list.mask:SetActive(self.data.is_buy)
    self.node_list.buy_flag:SetActive(self.data.is_buy)
end


----------------------------------------
-- 选择冲榜ItemRender
----------------------------------------
HelpRankSelectBtnItem = HelpRankSelectBtnItem or BaseClass(BaseRender)

function HelpRankSelectBtnItem:__init()
	
end

function HelpRankSelectBtnItem:LoadCallBack()
	
end

function HelpRankSelectBtnItem:OnSelectChange(is_select)
	self.node_list.img_hl:SetActive(is_select)
end

function HelpRankSelectBtnItem:OnFlush()
	if not self.data then
		return 
	end
    local data = self:GetData()
	
	local rank_name = data.rank_name
	self.node_list["lbl_name"].text.text = rank_name
	self.node_list["lbl_name_hl"].text.text = rank_name
	
	-- local free_reward_flag = HelpRankWGData.Instance:GetFreeRewardFlag()
	-- self.node_list["img_red"]:CustomSetActive(free_reward_flag == 0)
end