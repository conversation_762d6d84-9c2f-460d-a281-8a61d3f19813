LayoutZeroBuyView = LayoutZeroBuyView or BaseClass(SafeBaseView)

local MoneyType = {
	LingYu = 1,			-- 灵玉
	YuanBao = 2,		-- 元宝
	ZhiGou = 3,			-- 直购
	TongPiao = 4,		-- 通票
}

local Money_Id = { 
	[MoneyType.LingYu] = 65534,
	[MoneyType.YuanBao] = 65533,
	[MoneyType.ZhiGou] = 22099,
	[MoneyType.TongPiao] = 65540,
}

local Money_Icon = {
	[1] = "a3_huobi_xianyu",
	[2] = "a3_huobi_bangyu",
	[3] = "a3_huobi_score",
	[4] = "a3_huobi_cash_point",
}

local DEF_HIGHT = 72
local tian_shen = 5

-- 0 未上架, 1 准备上架（准备倒计时）, 2 上架未购买（新服倒计时）, 3 已购买（返现倒计时）, 4 下架
local WEISHANGJIA = 0
local ZHUNBEISHANGJIA = 1
local WEIGOUMAI = 2
local YIGOUMAI = 3
local XIAJIA = 4

function LayoutZeroBuyView:__init()
	self.view_style = ViewStyle.Half
	self:AddViewResource(0, "uis/view/zero_buy_ui_prefab", "layout_zerobuy_view")
	self:AddViewResource(0, ResPath.CommonBundleName, "layout_a3_light_common_top_panel")
	self:SetMaskBg()
end

function LayoutZeroBuyView:OpenCallBack()
	LayoutZeroBuyWGData.Instance:SetHasOpenZeroBuyFlag(true)
	RemindManager.Instance:Fire(RemindName.ZeroBuy)
	if PlayerPrefsUtil.GetString("FirstOpenZeroBuyView") ~= "FirstOpenZeroBuyView" then
		PlayerPrefsUtil.SetString("FirstOpenZeroBuyView", "FirstOpenZeroBuyView")
		TalkCache.StopCurIndexAudio()
		AudioManager.PlayAndForget(AudioWGData.Instance:GetOtherAutioEffect(AudioUrl.Sound22, nil, true))
	end
end

function LayoutZeroBuyView:ReleaseCallBack()
	if self.reward_list then
		self.reward_list:DeleteMe()
		self.reward_list = nil
	end

	if self.item_cell then
		self.item_cell:DeleteMe()
		self.item_cell = nil
	end

	if self.left_list_view then
		self.left_list_view:DeleteMe()
		self.left_list_view = nil
	end

	self.data = nil

	if self.display_model then
		self.display_model:DeleteMe()
		self.display_model = nil
	end

	if self.soul_ring_model then
        self.soul_ring_model:DeleteMe()
        self.soul_ring_model = nil
    end

	self:ClearCountDown()
	self.data_list = nil

	if self.money_bar then
		self.money_bar:DeleteMe()
		self.money_bar = nil
	end

	if self.node_list.buy_btn then
		XUI.SetButtonEnabled(self.node_list.buy_btn, true)
	end
	self.first_open_view = nil

	if LayoutZeroBuyWGCtrl.Instance then
		LayoutZeroBuyWGCtrl.Instance:SetZeroShowIndex(-1)
	end
end

function LayoutZeroBuyView:LoadCallBack()
	self.node_list.buy_btn.button:AddClickListener(BindTool.Bind(self.OnClickBuyBtn, self))
	self.node_list.special_buy_btn.button:AddClickListener(BindTool.Bind(self.OnClickBuyBtn, self))
	self.node_list.btn_all_privalige.button:AddClickListener(BindTool.Bind(self.OnClickBuyAllPrivalageBtn, self))

	self.left_list_view = AsyncListView.New(ZeroBuyCell, self.node_list.left_list_view)
	self.left_list_view:SetCellSizeDel(BindTool.Bind(self.CellSizeDel, self))
	self.left_list_view:SetSelectCallBack(BindTool.Bind(self.SelectCallBack, self))
	self.item_cell = ItemCell.New(self.node_list.reward_item)

	if not self.reward_list then
		self.reward_list = AsyncListView.New(ItemCell, self.node_list.reward_list)
		self.reward_list:SetStartZeroIndex(true)
	end

	if not self.display_model then
		self.display_model = OperationActRender.New(self.node_list.display_model)
		self.display_model:SetModelType(MODEL_CAMERA_TYPE.BASE, MODEL_OFFSET_TYPE.NORMALIZE)
	end

	if not self.soul_ring_model then
		self.soul_ring_model = RoleModel.New()
		local display_data = {
			parent_node = self.node_list["soul_ring_model"],
			camera_type = MODEL_CAMERA_TYPE.BASE,
			rt_scale_type = ModelRTSCaleType.L,
			can_drag = false,
		}

		self.soul_ring_model:SetRenderTexUI3DModel(display_data)

		local role_vo = GameVoManager.Instance:GetMainRoleVo()
		local special_status_table = {ignore_wing = true, ignore_halo = true, ignore_fazhen = true, ignore_mantle = true, ignore_tail = true, ignore_jianzhen = true}
		self.soul_ring_model:SetModelResInfo(role_vo, special_status_table)
    end

	if not self.money_bar then
		self.money_bar = MoneyBar.New()
		local bundle, asset = ResPath.GetWidgets("MoneyBar")
		local show_params = {
			show_gold = true,
			show_bind_gold = true,
			show_coin = true,
			show_silver_ticket = true,
		}
		self.money_bar:SetMoneyShowInfo(0, 0, show_params)
		self.money_bar:LoadAsset(bundle, asset, self.node_list["money_tabar_pos"].transform)
	end

	LayoutZeroBuyWGData.Instance:SetIsFristOpen(0)
	RemindManager.Instance:Fire(RemindName.ZeroBuy)

	LayoutZeroBuyWGCtrl.Instance:SendCSZeroBuyReq(ZERO_BUY_OPERA_TYPE.INFO)
	self.first_open_view = true

	for i = 1, 3 do
		self.node_list["sp_boss_tip" .. i].text.text = Language.ZeroBuy["DescSPBossTip" .. i]
	end
end

-- 切换标签调用
function LayoutZeroBuyView:ShowIndexCallBack(index)
	self.data_list = LayoutZeroBuyWGData.Instance:GetZeroBuyData()
	if self.left_list_view then
		local default_index = LayoutZeroBuyWGCtrl.Instance:GetZeroShowIndex() --先检测是不是下架超链接点击过来的
		if not default_index or default_index < 0 then
			default_index = LayoutZeroBuyWGData.Instance:GetCanFetchIndex(self.data_list)
		end
		self.left_list_view:SetDataList(self.data_list)
		self.data = self.data_list[default_index or 1]
		self.left_list_view:JumpToIndex(default_index or 1)
	end
end

function LayoutZeroBuyView:CellSizeDel(index)
	return DEF_HIGHT
end

function LayoutZeroBuyView:OnFlush(param_t)
	if not self.data_list then
		return
	end

	if not self.first_open_view then
		self.left_list_view:SetDataList(self.data_list)
	end
	self.first_open_view = false

	for k, v in pairs(param_t) do
		if k == "all" then
			if v.open_param and tonumber(v.open_param) then
				local cacular_index = tonumber(v.open_param)
				for k, v in pairs(self.data_list) do
					if v.cfg.ID == cacular_index then
						cacular_index = k
						break
					end
				end
				self.left_list_view:JumpToIndex(cacular_index)
			end
		end
	end

	if not self.data then
		return
	end

	self:FlushRightPanel()
end

function LayoutZeroBuyView:FlushRightPanel()
	if not self.data then
		return
	end


	self.node_list.normal_root:CustomSetActive(self.data.special_sign ~= 1)
	self.node_list.special_root:CustomSetActive(self.data.special_sign == 1)

	if self.data.special_sign == 1 then
		local data = LayoutZeroBuyWGData.Instance:GetZeroBuyDataByID(self.data.cfg.ID)
		if not data or IsEmptyTable(data) then
			return
		end

		self.data_info = data
		if data.new_flag == 1 then
			LayoutZeroBuyWGCtrl.Instance:SendCSZeroBuyReq(ZERO_BUY_OPERA_TYPE.SET_NEW_FLAG, data.cfg.ID)
		end

		self.node_list["sp_money_icon"]:SetActive(data.cfg.money_type ~= 3)
		self.node_list["sp_money_icon"].image:LoadSprite(ResPath.GetCommonIcon(Money_Icon[data.cfg.money_type]))

		local show_red_point = false
		if data.state == WEIGOUMAI or data.state == ZHUNBEISHANGJIA or data.state == WEISHANGJIA then
			local price_str = data.cfg.money_type == 3 and RoleWGData.GetPayMoneyStr(data.cfg.buy_cost, data.cfg.rmb_type, self.data.rmb_seq) or data.cfg.buy_cost
			self.node_list.sp_buy_btn_text.text.text = price_str
			self.node_list.desc_sp_buy_state.text.text = Language.ZeroBuy.BtnText_3
		else
			--如果买了，就显示买的时候的数据
			self.node_list["sp_money_icon"]:SetActive(true)
			local money_num = 0
			money_num = data.money_type == 0 and data.cfg.buy_cost or data.buy_cost
			self.node_list.sp_buy_btn_text.text.text = Language.ZeroBuy.BtnText_2 .. " " .. money_num
			self.node_list.desc_sp_buy_state.text.text = ""
			show_red_point = true
		end

		if data.state == XIAJIA then
			self.node_list.special_fetched_flag:SetActive(true)
			self.node_list.special_buy_btn:SetActive(false)
		else
			self.time = data.timestamp
			local buy_flag = (data.state ~= WEIGOUMAI and (self.time - TimeWGCtrl.Instance:GetServerTime()) <= 0) or data.state == WEIGOUMAI
			--绑定仙玉足够购买0元购的第一个道具时，0元购增加一个红点提示,以及稀世天神增加一个红点提示
			local bimai_red = false
			if data.cfg.money_type == 2 then
				local bind_gold = RoleWGData.Instance:GetAttr("bind_gold")
				bimai_red = bind_gold >= data.cfg.buy_cost and data.state == WEIGOUMAI
			end

			XUI.SetButtonEnabled(self.node_list.special_buy_btn, buy_flag)

			self.node_list.special_fetched_flag:SetActive(false)
			self.node_list.special_buy_btn:SetActive(true)
			show_red_point = (show_red_point and buy_flag) or bimai_red
		end
	
		self.node_list.buy_btn_redpoint:SetActive(show_red_point)
	else
		local data = LayoutZeroBuyWGData.Instance:GetZeroBuyDataByID(self.data.cfg.ID)
		if not data or IsEmptyTable(data) then
			return
		end
		self.data_info = data
	
		if data.new_flag == 1 then
			LayoutZeroBuyWGCtrl.Instance:SendCSZeroBuyReq(ZERO_BUY_OPERA_TYPE.SET_NEW_FLAG, data.cfg.ID)
		end

		self.node_list["money_icon"]:SetActive(data.cfg.money_type ~= 3)
		self.node_list["money_icon"].image:LoadSprite(ResPath.GetCommonIcon(Money_Icon[data.cfg.money_type]))

		local cur_open_day = TimeWGCtrl.Instance:GetCurOpenServerDay()
		local back_time = data.cfg.reward_time -- / 24
		local left_day = back_time - cur_open_day
		if left_day <= 0 then
			self.node_list.desc_1.text.text = Language.ZeroBuy.DescText4
		else
			self.node_list.desc_1.text.text = string.format(Language.ZeroBuy.DescText5, left_day)
		end

		local item_data_list = data.cfg.com_reward or {}
		local item_capability = ItemShowWGData.CalculateCapability(data.cfg.model_id, false)
		if not IsEmptyTable(item_data_list) then
			for k, v in pairs(item_data_list) do
				if v.item_id ~= data.cfg.model_id then
					item_capability = item_capability + ItemShowWGData.CalculateCapability(v.item_id, false)
				end
			end
			self.reward_list:SetDataList(item_data_list)
		end

		-- -- 自选礼包问题，导致没战力 策划让拿一下模型展示道具计算战力
		-- if item_capability <= 0 then
		-- 	item_capability = item_capability + ItemShowWGData.CalculateCapability(data.cfg.model_id, false)
		-- end

		self.node_list.cap_value.text.text = item_capability
		self.node_list.desc_get_cap.text.text = string.format(Language.ZeroBuy.DescGetcap, CommonDataManager.ConverExpByThousand(item_capability))

		self:FlushModel(data)

		local show_red_point = false
		local cur_money = Money_Id[data.cfg.money_type]
		if data.state == WEIGOUMAI or data.state == ZHUNBEISHANGJIA or data.state == WEISHANGJIA then
			local price_str = data.cfg.money_type == 3 and RoleWGData.GetPayMoneyStr(data.cfg.buy_cost, data.cfg.rmb_type, self.data.rmb_seq) or data.cfg.buy_cost
			self.node_list.buy_btn_text.text.text = price_str
			self.node_list.desc_buy_state.text.text = Language.ZeroBuy.BtnText_1
			self.item_cell:SetData({ item_id = cur_money, num = data.cfg.back_num })
		else
			--如果买了，就显示买的时候的数据
			self.node_list["money_icon"]:SetActive(true)
			local money_num = data.money_type == 0 and data.cfg.buy_cost or data.buy_cost
			self.node_list.buy_btn_text.text.text = Language.ZeroBuy.BtnText_2 .. " " .. money_num
			self.node_list.desc_buy_state.text.text = Language.ZeroBuy.BtnText_2
			self.item_cell:SetData({ item_id = cur_money, num = data.cfg.back_num })

			show_red_point = true
		end

		--折扣.
		local discount = data.cfg.discount
		local is_show_dis = nil ~= discount and "" ~= discount and (data.state == WEIGOUMAI or data.state == ZHUNBEISHANGJIA)
		self.node_list.discount_img:SetActive(is_show_dis)
		if is_show_dis then
			self.node_list.discount_text.text.text = string.format(Language.ZeroBuy.DiscountText, discount)
		end

		self.node_list.remain_time:SetActive(false)
		self:ClearCountDown()
		if data.state == XIAJIA then
			self.node_list.fetched_flag:SetActive(true)
			self.node_list.buy_btn:SetActive(false)
			self.node_list.back_flag:SetActive(false)
		else
			self.time = data.timestamp
			local buy_flag = (data.state ~= WEIGOUMAI and (self.time - TimeWGCtrl.Instance:GetServerTime()) <= 0) or data.state == WEIGOUMAI
			--绑定仙玉足够购买0元购的第一个道具时，0元购增加一个红点提示,以及稀世天神增加一个红点提示
			local bimai_red = false
			if data.cfg.money_type == 2 then
				local bind_gold = RoleWGData.Instance:GetAttr("bind_gold")
				bimai_red = bind_gold >= data.cfg.buy_cost and data.state == WEIGOUMAI
			end

			XUI.SetButtonEnabled(self.node_list.buy_btn, buy_flag)

			self.node_list.fetched_flag:SetActive(false)
			self.node_list.buy_btn:SetActive(true)
			self.node_list.back_flag:SetActive(data.state ~= WEIGOUMAI and (self.time - TimeWGCtrl.Instance:GetServerTime()) <= 0)

			show_red_point = (show_red_point and buy_flag) or bimai_red
			if self.time - TimeWGCtrl.Instance:GetServerTime() > 0 then
				if not self.quest and not self.is_complete then
					self:UpdateCallBack()
					self.quest = GlobalTimerQuest:AddRunQuest(BindTool.Bind1(self.UpdateCallBack, self), 1)
				end
			end
		end

		self.node_list.buy_btn_redpoint:SetActive(show_red_point)
	end
end

function LayoutZeroBuyView:SelectCallBack(item_cell)
	local data = item_cell:GetData()
	if not data then
		return
	end

	if data.id == tian_shen then
		LayoutZeroBuyWGData.Instance:SetZeroBuyFlag(false)
		RemindManager.Instance:Fire(RemindName.ZeroBuy)
	end

	if data.special_sign ~= 1 then
		local bundle, asset = ResPath.GetRawImagesPNG(data.cfg.title_show_img)
		self.node_list["zerobuy_img"].raw_image:LoadSprite(bundle, asset, function()
			self.node_list["zerobuy_img"].raw_image:SetNativeSize()
		end)

		bundle, asset = ResPath.GetRawImagesPNG(data.cfg.reward_desc_img)
		self.node_list["reward_desc_img"].raw_image:LoadSprite(bundle, asset, function()
			self.node_list["reward_desc_img"].raw_image:SetNativeSize()
		end)

		self.node_list.active_jingshen_img:SetActive(false)
		if data.cfg.model_id and data.cfg.model_id ~= "" then
			local beast_cfg = ControlBeastsWGData.Instance:GetBeastCfgById(data.cfg.model_id)
			if beast_cfg then
				self.node_list.active_jingshen_img:SetActive(true)
				bundle, asset = ResPath.GetCommonImages(string.format("a3_hs_pz%d", beast_cfg.beast_color))
				self.node_list.active_jingshen_img.image:LoadSprite(bundle, asset, function()
					self.node_list.active_jingshen_img.image:SetNativeSize()
				end)
			end
		end
	end

	self.data = data
	self.is_complete = nil
	self:Flush()
end

function LayoutZeroBuyView:UpdateCallBack(elapse_time, total_time)
	local time = self.time - TimeWGCtrl.Instance:GetServerTime()
	if time > 0 then
		if self.data_info and self.data_info.state > 0 and self.data_info.state < 4 then
			self.node_list.remain_time.text.text = string.format(Language.ZeroBuy.RemainTimeText_1[self.data_info.state],
				TimeUtil.FormatSecondDHM6(time))
			if not self.node_list.remain_time.activeSelf then
				self.node_list.remain_time:SetActive(true)
			end
		end
	else
		self:CompleteCallBack()
	end
end

function LayoutZeroBuyView:CompleteCallBack()
	self:ClearCountDown()
	self.node_list.remain_time:SetActive(false)
	self.is_complete = true
	self:Flush()
end

function LayoutZeroBuyView:ClearCountDown()
	GlobalTimerQuest:CancelQuest(self.quest)
	self.quest = nil
end

-- 关闭前调用
function LayoutZeroBuyView:CloseCallBack()
	self.select_item_cell = nil
	self.first_open_view = true
end

function LayoutZeroBuyView:OnClickBuyBtn()
	if not self.data then
		return
	end
	local data_info = LayoutZeroBuyWGData.Instance:GetZeroBuyDataByID(self.data.cfg.ID)
	if not data_info then
		return
	end

	local cur_time = TimeWGCtrl.Instance:GetServerTime()
	--可领取
	if data_info.state == YIGOUMAI and cur_time > data_info.timestamp then
		--领取
		LayoutZeroBuyWGCtrl.Instance:SendCSZeroBuyReq(ZERO_BUY_OPERA_TYPE.FETCH, data_info.cfg.ID)
	else
		--购买
		if data_info.cfg.money_type == 3 then -- 直购特殊处理
			RechargeWGCtrl.Instance:Recharge(data_info.cfg.buy_cost, data_info.cfg.rmb_type, data_info.cfg.rmb_seq)
		else
			LayoutZeroBuyWGCtrl.Instance:SendCSZeroBuyReq(ZERO_BUY_OPERA_TYPE.BUY, data_info.cfg.ID)
		end
	end
end

function LayoutZeroBuyView:FlushModel(data)
	local model_cfg = data.cfg
	if model_cfg.soul_ring_id and model_cfg.soul_ring_id ~= "" then
		self.node_list.soul_ring_model:SetActive(true)
        self.node_list.display_model:SetActive(false)

		if model_cfg.model_pos and model_cfg.model_pos ~= "" then
			local pos_list = string.split(model_cfg.model_pos, "|")
			local pos_x = tonumber(pos_list[1]) or 0
			local pos_y = tonumber(pos_list[2]) or 0
			local pos_z = tonumber(pos_list[3]) or 0

			self.soul_ring_model:SetRTAdjustmentRootLocalPosition(pos_x, pos_y, pos_z)
		end

		if model_cfg.model_rot and "" ~= model_cfg.model_rot then
			local pos_list = string.split(model_cfg.model_rot, "|")
			local x = tonumber(pos_list[1]) or 0
			local y = tonumber(pos_list[2]) or 0
			local z = tonumber(pos_list[3]) or 0

			self.soul_ring_model:SetRTAdjustmentRootLocalRotation(x, y, z)
		end

		if model_cfg.model_scale and "" ~= model_cfg.model_scale then
			self.soul_ring_model:SetRTAdjustmentRootLocalScale(model_cfg.model_scale, model_cfg.model_scale, model_cfg.model_scale)
		end

        local target_data = {}
        if model_cfg.soul_ring_id and "" ~= model_cfg.soul_ring_id then
			local soul_ring_id_list = string.split(model_cfg.soul_ring_id, "|")
            for k, v in pairs(soul_ring_id_list) do
                local cfg = ShenShouWGData.Instance:GetShenShouCfg(tonumber(v))
                target_data[k - 1] = {soul_ring_effect = cfg.soul_ring_effect}
            end

            self.soul_ring_model:SetTotalSoulRingResid(target_data, false, #soul_ring_id_list)
		end
	else
		self.node_list.soul_ring_model:SetActive(false)
        self.node_list.display_model:SetActive(true)
		local display_data = {}
		display_data.should_ani = true
		if model_cfg.model_id ~= 0 and model_cfg.model_id ~= "" then
			local split_list = string.split(model_cfg.model_id, "|")
			if #split_list > 1 then
				local list = {}
				for k, v in pairs(split_list) do
					list[tonumber(v)] = true
				end
				display_data.model_item_id_list = list
			else
				display_data.item_id = model_cfg.model_id
			end
		end

		display_data.bundle_name = model_cfg["model_bundle_name"]
    	display_data.asset_name = model_cfg["model_asset_name"]
		display_data.render_type = model_cfg.show_type -1
		display_data.model_rt_type = ModelRTSCaleType.L
		display_data.can_drag = false

		if model_cfg.model_pos and model_cfg.model_pos ~= "" then
			local pos_list = string.split(model_cfg.model_pos, "|")
			local pos_x = tonumber(pos_list[1]) or 0
			local pos_y = tonumber(pos_list[2]) or 0
			local pos_z = tonumber(pos_list[3]) or 0

			display_data.model_adjust_root_local_position = Vector3(pos_x, pos_y, pos_z)
		end

		if model_cfg.model_rot and model_cfg.model_rot ~= "" then
			local rot_list = string.split(model_cfg.model_rot, "|")
			local rot_x = tonumber(rot_list[1]) or 0
			local rot_y = tonumber(rot_list[2]) or 0
			local rot_z = tonumber(rot_list[3]) or 0

			display_data.model_adjust_root_local_rotation = Vector3(rot_x, rot_y, rot_z)
		end

		display_data.model_adjust_root_local_scale = model_cfg.model_scale
		self.display_model:SetData(display_data)
	end
end

function LayoutZeroBuyView:OnClickBuyAllPrivalageBtn()
	ViewManager.Instance:Open(GuideModuleName.Vip, TabIndex.recharge_vip, "vip_level", {vip_level = 6})
end

--------------------------------------------------------------------------ZeroBuyCell---------------------------------------------------------------------------
ZeroBuyCell = ZeroBuyCell or BaseClass(BaseRender)

function ZeroBuyCell:__init()
	self:OnSelectChange(false)
end

function ZeroBuyCell:__delete()
	self:ClearCellCountDown()
end

function ZeroBuyCell:OnFlush()
	if not self.data then
		return
	end

	self.node_list.name.text.text = string.format(Language.ZeroBuy.CellTitle, self.data.cfg.back_num)
	self.node_list.cell_name.text.text = self.data.cfg.item_name
	self.node_list.sp_cell_name.text.text = self.data.cfg.item_name
	local icon_res = Money_Icon[self.data.cfg.money_type]-- ResPath.GetMoneyIcon(self.data.cfg.money_type)
	self.node_list.cost_icon.image:LoadSprite(ResPath.GetCommonIcon(icon_res))
	self.node_list.cost_icon.image:SetNativeSize()
	local data_info = LayoutZeroBuyWGData.Instance:GetZeroBuyDataByID(self.data.cfg.ID)
	if not data_info then
		return
	end

	local flag = LayoutZeroBuyWGData.Instance:GetZeroBuyFlag()
	self.node_list.new_xin_img:SetActive(data_info.new_flag == 1)
	local time = data_info.timestamp - TimeWGCtrl.Instance:GetServerTime()
	local show_red = false
	--绑定仙玉足够购买0元购的第一个道具时，0元购增加一个红点提示以及稀世天神增加一个红点提示
	if self.data.cfg.money_type == 2 then
		local bind_gold = RoleWGData.Instance:GetAttr("bind_gold")
		show_red = bind_gold >= self.data.cfg.buy_cost and data_info.state == WEIGOUMAI
	end
	self.node_list.remind:SetActive((data_info.state == YIGOUMAI and time < 0) or show_red or
	(flag and self.data.id == 5))
	self:ClearCellCountDown()
	if time <= 0 then
		self.node_list.remain_time:SetActive(false)
	else
		if data_info.state == YIGOUMAI then
			self.node_list.remain_time:SetActive(true)
			self:CellUpdateCallBack(0, time)
			self.count_down = CountDown.Instance:AddCountDown(time, 1, BindTool.Bind(self.CellUpdateCallBack, self),
				BindTool.Bind(self.CellCompleteCallBack, self))
		else
			self.node_list.remain_time:SetActive(false)
		end
	end
end

function ZeroBuyCell:CellUpdateCallBack(elapse_time, total_time)
	local time = math.floor(total_time - elapse_time)
	self.node_list.remain_time:SetActive(time > 0)
	if time > 0 then
		self.node_list.remain_time.text.text = TimeUtil.FormatTimeLanguage3(time)
	end
end

function ZeroBuyCell:CellCompleteCallBack()
	self:ClearCellCountDown()
	self.node_list.remain_time:SetActive(false)
end

function ZeroBuyCell:ClearCellCountDown()
	if CountDown.Instance:HasCountDown(self.count_down) then
		CountDown.Instance:RemoveCountDown(self.count_down)
		self.count_down = nil
	end
end

function ZeroBuyCell:OnSelectChange(is_select)
	local special_sign = self.data and self.data.special_sign or 0

	self.node_list.bg_hl:CustomSetActive(special_sign ~= 1 and is_select)
	self.node_list.sp_bg_hl:CustomSetActive(special_sign == 1 and is_select)

	self.node_list.cell_name:CustomSetActive(special_sign ~= 1)
	self.node_list.sp_cell_name:CustomSetActive(special_sign == 1)

	if special_sign ~= 1 then
		local color = is_select and COLOR3B.C22 or "#ba986b"
		self.node_list.cell_name.text.color = Str2C3b(color)
		self.node_list.name.text.color = Str2C3b(color)
	else
		local color = is_select and COLOR3B.C22 or "#ba986b"
		self.node_list.sp_cell_name.text.color = Str2C3b(color)
		self.node_list.name.text.color = Str2C3b(color)
	end
end
